import datetime
import hashlib
import os
from src.utils.MinioUtils import Helper
def get_file_info(folder_path):
    file_info_list = []
    for root, dirs, files in os.walk(folder_path):
        for file in files:
            file_path = os.path.join(root, file)
            file_info_list.append((file, file_path))
    return file_info_list

def encryption(signature_data):
    signature_md5 = hashlib.md5()
    signature_md5.update(signature_data.encode("utf-8"))
    signature = signature_md5.hexdigest()
    return signature


if __name__ == '__main__':
    # 获取文件夹下的所有图片文件
    folder_path = r'/Users/<USER>/Downloads/botSmart-日常业务/app巡查/文件处理/投屏和激励广告20250814/投屏广告'  # 可替换为实际文件夹路径
    file_info = get_file_info(folder_path)
    for file_name, file_path in file_info:
        image_url = Helper.save_image_file_by_path(save_name='/mdd/gg/投屏/'+encryption(file_name + datetime.datetime.now().strftime("%Y%m%d_%H%M%S.%f"))+'.'+file_name.split('.')[-1], file_name=file_path)
        print(image_url)
        os.remove(file_path)
        kp_t = open(r'/Users/<USER>/Downloads/botSmart-日常业务/app巡查/文件处理/投屏和激励广告20250814/'+datetime.datetime.now().strftime("%Y%m%d_")+'url.csv', mode='a+')
        kp_t.write(image_url + '\n')
        kp_t.close()
    folder_path = r'/Users/<USER>/Downloads/botSmart-日常业务/app巡查/文件处理/投屏和激励广告20250814/激励广告'  # 可替换为实际文件夹路径
    file_info = get_file_info(folder_path)
    for file_name, file_path in file_info:
        image_url = Helper.save_image_file_by_path(save_name='/mdd/gg/激励/'+encryption(file_name + datetime.datetime.now().strftime("%Y%m%d_%H%M%S.%f"))+'.'+file_name.split('.')[-1], file_name=file_path)
        print(image_url)
        os.remove(file_path)
        kp_t = open(r'/Users/<USER>/Downloads/botSmart-日常业务/app巡查/文件处理/投屏和激励广告20250814/'+datetime.datetime.now().strftime("%Y%m%d_")+'url.csv', mode='a+')
        kp_t.write(image_url + '\n')
        kp_t.close()