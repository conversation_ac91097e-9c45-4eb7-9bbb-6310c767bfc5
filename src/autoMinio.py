# 在这个文件内编写代码开发一个界面化的python程序，使用Tkinter开发；
# 功能参照mdd_image_upload.py里面的功能，界面上选择一个文件夹，然后将文件夹下的所有图片上传到minio服务器上；
# 上传完成后，将所有图片的url保存到一个csv文件中，文件名为当前日期.csv，保存到当前文件夹下；
# 上传完成后，将所有图片的url打印到控制台上；
# 上传完成后，将所有图片的url保存到剪贴板中；
import tkinter as tk
from tkinter import filedialog
from src.utils.MinioUtils import Helper
import os
import datetime
import hashlib

def get_file_info(folder_path):
    """获取指定文件夹下所有图片文件的信息"""
    image_extensions = ['.jpg', '.jpeg', '.png', '.gif', '.bmp']
    file_info_list = []
    for root, _, files in os.walk(folder_path):
        for file in files:
            file_ext = os.path.splitext(file)[1].lower()
            if file_ext in image_extensions:
                file_path = os.path.join(root, file)
                file_info_list.append((file, file_path))
    return file_info_list

def encryption(signature_data):
    """MD5加密函数"""
    signature_md5 = hashlib.md5()
    signature_md5.update(signature_data.encode("utf-8"))
    signature = signature_md5.hexdigest()
    return signature

class App:
    def __init__(self, master):
        self.master = master
        self.master.title("Minio Upload")
        self.master.geometry("300x200")

        self.label = tk.Label(self.master, text="选择文件夹:")
        self.label.pack()

        self.button = tk.Button(self.master, text="选择", command=self.select_folder)
        self.button.pack()

    def select_folder(self):
        folder_path = filedialog.askdirectory()
        if folder_path:
            self.upload_images(folder_path)

    def upload_images(self, folder_path):
        file_info = get_file_info(folder_path)
        for file_name, file_path in file_info:
            image_url = Helper.save_image_file_by_path(save_name='/mdd/gg/投屏/'+encryption(file_name + datetime.datetime.now().strftime("%Y%m%d_%H%M%S.%f"))+'.'+file_name.split('.')[-1], file_name=file_path)
            print(image_url)
            os.remove(file_path)
            kp_t = open(r'/Users/<USER>/Downloads/botSmart-日常业务/app巡查/文件处理/投屏和激励广告20250814/'+datetime.datetime.now().strftime("%Y%m%d_")+'url.csv', mode='a+')
            kp_t.write(image_url + '\n')
            kp_t.close()