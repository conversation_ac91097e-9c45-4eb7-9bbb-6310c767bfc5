# 在这个文件内编写代码开发一个界面化的python程序，使用Tkinter开发；
# 功能参照mdd_image_upload.py里面的功能，界面上选择一个文件夹，然后将文件夹下的所有图片上传到minio服务器上；
# 上传完成后，将所有图片的url保存到一个csv文件中，文件名为当前日期.csv，保存到当前文件夹下；
# 上传完成后，将所有图片的url打印到控制台上；
# 上传完成后，将所有图片的url保存到剪贴板中；
import tkinter as tk
from tkinter import filedialog, messagebox, scrolledtext
from src.utils.MinioUtils import Helper
import os
import datetime
import hashlib
import csv
from pathlib import Path

def get_file_info(folder_path):
    """获取指定文件夹下所有图片文件的信息"""
    image_extensions = ['.jpg', '.jpeg', '.png', '.gif', '.bmp']
    file_info_list = []
    for root, _, files in os.walk(folder_path):
        for file in files:
            file_ext = os.path.splitext(file)[1].lower()
            if file_ext in image_extensions:
                file_path = os.path.join(root, file)
                file_info_list.append((file, file_path))
    return file_info_list

def encryption(signature_data):
    """MD5加密函数"""
    signature_md5 = hashlib.md5()
    signature_md5.update(signature_data.encode("utf-8"))
    signature = signature_md5.hexdigest()
    return signature

class App:
    def __init__(self, master):
        self.master = master
        self.master.title("Minio图片上传工具")
        self.master.geometry("800x600")

        # 存储上传的URL列表
        self.uploaded_urls = []

        self.setup_ui()

    def setup_ui(self):
        # 主框架
        main_frame = tk.Frame(self.master)
        main_frame.pack(fill=tk.BOTH, expand=True, padx=10, pady=10)

        # 配置区域
        config_frame = tk.LabelFrame(main_frame, text="配置", padx=5, pady=5)
        config_frame.pack(fill=tk.X, pady=(0, 10))

        # 目录前缀设置
        tk.Label(config_frame, text="目录前缀:").grid(row=0, column=0, sticky=tk.W, padx=(0, 5))
        self.prefix_var = tk.StringVar(value="/mdd/gg")
        self.prefix_entry = tk.Entry(config_frame, textvariable=self.prefix_var, width=30)
        self.prefix_entry.grid(row=0, column=1, sticky=tk.W, padx=(0, 10))

        # CSV保存路径设置
        tk.Label(config_frame, text="CSV保存路径:").grid(row=1, column=0, sticky=tk.W, padx=(0, 5))
        self.csv_path_var = tk.StringVar(value=os.getcwd())
        self.csv_path_entry = tk.Entry(config_frame, textvariable=self.csv_path_var, width=40)
        self.csv_path_entry.grid(row=1, column=1, sticky=tk.W, padx=(0, 5))
        tk.Button(config_frame, text="浏览", command=self.select_csv_path).grid(row=1, column=2)

        # 操作区域
        operation_frame = tk.LabelFrame(main_frame, text="操作", padx=5, pady=5)
        operation_frame.pack(fill=tk.X, pady=(0, 10))

        # 选择文件夹
        tk.Label(operation_frame, text="选择图片文件夹:").grid(row=0, column=0, sticky=tk.W, padx=(0, 5))
        self.folder_var = tk.StringVar()
        self.folder_entry = tk.Entry(operation_frame, textvariable=self.folder_var, width=50, state='readonly')
        self.folder_entry.grid(row=0, column=1, sticky=tk.W, padx=(0, 5))
        tk.Button(operation_frame, text="选择文件夹", command=self.select_folder).grid(row=0, column=2, padx=(5, 0))

        # 按钮区域
        button_frame = tk.Frame(operation_frame)
        button_frame.grid(row=1, column=0, columnspan=3, pady=(10, 0))

        self.upload_button = tk.Button(button_frame, text="开始上传", command=self.start_upload,
                                     bg="#4CAF50", fg="white", font=("Arial", 10, "bold"))
        self.upload_button.pack(side=tk.LEFT, padx=(0, 10))

        self.export_button = tk.Button(button_frame, text="导出CSV", command=self.export_csv,
                                     bg="#2196F3", fg="white", font=("Arial", 10, "bold"))
        self.export_button.pack(side=tk.LEFT, padx=(0, 10))

        self.clear_button = tk.Button(button_frame, text="清空日志", command=self.clear_log,
                                    bg="#FF9800", fg="white", font=("Arial", 10, "bold"))
        self.clear_button.pack(side=tk.LEFT)

        # 日志显示区域
        log_frame = tk.LabelFrame(main_frame, text="操作日志", padx=5, pady=5)
        log_frame.pack(fill=tk.BOTH, expand=True)

        self.log_text = scrolledtext.ScrolledText(log_frame, height=20, width=80)
        self.log_text.pack(fill=tk.BOTH, expand=True)

    def log_message(self, message):
        """在日志区域添加消息"""
        timestamp = datetime.datetime.now().strftime("%H:%M:%S")
        self.log_text.insert(tk.END, f"[{timestamp}] {message}\n")
        self.log_text.see(tk.END)
        self.master.update()

    def select_csv_path(self):
        """选择CSV保存路径"""
        path = filedialog.askdirectory()
        if path:
            self.csv_path_var.set(path)

    def select_folder(self):
        """选择图片文件夹"""
        folder_path = filedialog.askdirectory()
        if folder_path:
            self.folder_var.set(folder_path)
            self.log_message(f"已选择文件夹: {folder_path}")

    def determine_category(self, file_path):
        """根据文件路径确定分类（投屏/激励）"""
        path_parts = Path(file_path).parts
        for part in path_parts:
            if "投屏" in part:
                return "投屏"
            elif "激励" in part:
                return "激励"
        return "其他"

    def start_upload(self):
        """开始上传图片"""
        folder_path = self.folder_var.get()
        if not folder_path:
            messagebox.showwarning("警告", "请先选择图片文件夹！")
            return

        if not os.path.exists(folder_path):
            messagebox.showerror("错误", "选择的文件夹不存在！")
            return

        prefix = self.prefix_var.get().strip()
        if not prefix:
            messagebox.showwarning("警告", "请输入目录前缀！")
            return

        self.log_message("开始扫描图片文件...")
        file_info = get_file_info(folder_path)

        if not file_info:
            self.log_message("未找到图片文件！")
            messagebox.showinfo("提示", "在选择的文件夹中未找到图片文件！")
            return

        self.log_message(f"找到 {len(file_info)} 个图片文件，开始上传...")

        success_count = 0
        for file_name, file_path in file_info:
            try:
                # 确定分类
                category = self.determine_category(file_path)

                # 生成保存名称
                timestamp = datetime.datetime.now().strftime("%Y%m%d_%H%M%S.%f")
                encrypted_name = encryption(file_name + timestamp)
                file_ext = file_name.split('.')[-1]
                save_name = f"{prefix}/{category}/{encrypted_name}.{file_ext}"

                # 上传图片
                self.log_message(f"正在上传: {file_name} -> {category}")
                image_url = Helper.save_image_file_by_path(save_name=save_name, file_name=file_path)

                if image_url:
                    self.uploaded_urls.append({
                        'original_name': file_name,
                        'category': category,
                        'url': image_url,
                        'upload_time': datetime.datetime.now().strftime("%Y-%m-%d %H:%M:%S")
                    })

                    self.log_message(f"上传成功: {image_url}")

                    # 删除原文件
                    os.remove(file_path)
                    self.log_message(f"已删除原文件: {file_path}")

                    success_count += 1
                else:
                    self.log_message(f"上传失败: {file_name}")

            except Exception as e:
                self.log_message(f"处理文件 {file_name} 时出错: {str(e)}")
                continue

        self.log_message(f"上传完成！成功上传 {success_count} 个文件")
        messagebox.showinfo("完成", f"上传完成！成功上传 {success_count} 个文件")

    def export_csv(self):
        """导出CSV文件"""
        if not self.uploaded_urls:
            messagebox.showwarning("警告", "没有可导出的数据！")
            return

        csv_path = self.csv_path_var.get()
        if not csv_path or not os.path.exists(csv_path):
            messagebox.showerror("错误", "CSV保存路径不存在！")
            return

        # 生成CSV文件名
        timestamp = datetime.datetime.now().strftime("%Y%m%d_%H%M%S")
        csv_filename = f"uploaded_urls_{timestamp}.csv"
        csv_filepath = os.path.join(csv_path, csv_filename)

        try:
            with open(csv_filepath, 'w', newline='', encoding='utf-8') as csvfile:
                fieldnames = ['original_name', 'category', 'url', 'upload_time']
                writer = csv.DictWriter(csvfile, fieldnames=fieldnames)

                writer.writeheader()
                for url_info in self.uploaded_urls:
                    writer.writerow(url_info)

            self.log_message(f"CSV文件已导出: {csv_filepath}")
            messagebox.showinfo("成功", f"CSV文件已导出到:\n{csv_filepath}")

        except Exception as e:
            self.log_message(f"导出CSV失败: {str(e)}")
            messagebox.showerror("错误", f"导出CSV失败: {str(e)}")

    def clear_log(self):
        """清空日志"""
        self.log_text.delete(1.0, tk.END)
        self.log_message("日志已清空")

if __name__ == '__main__':
    root = tk.Tk()
    app = App(root)
    root.mainloop()