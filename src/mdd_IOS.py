# -*- coding: utf-8 -*-
import base64
import datetime
import hashlib
import os
import pathlib
import random
import time
import traceback
import io
import cv2
import wda

from src.utils import IOSWatcherUtils
from src.utils import MysqlHelper
from src.utils.MinioUtils import Helper

udid = '00008101-000534A93A40001E'
wda_proxy = 'com.facebook.WebDriverAgentRunner.cx2622.xctrunner'
port = '9401'
folderpath = r'/Users/<USER>/Downloads/botSmart-日常业务/app巡查/mdd/'
sid = ''
pr_origin = '1'
pr_hit_type = '0'
pr_hit_url = ''
pr_screen_time = '%Y-%m-%d %H:%M:%S'
pr_module_name = '广告'
pr_status = '0'
pr_repair = '0'
pr_operator_sid = 'kGNzHYK6I9srBgf8X9PbKVrGpgKk4jry2XVs'
pr_create_time = '%Y-%m-%d %H:%M:%S'
pr_update_time = '%Y-%m-%d %H:%M:%S'
pr_deleted = '0'
pr_ti_sid = '9bb2acb81e56819db353600030683b7a'
pr_cpi_sid = '67e7d810d77c15bb379f39f411a5bad5'
pr_ai_sid = '55b6a12e3e593e427402b0615fb9aa1c'
pr_ci_sid = '7a124f45e63416eeb6e8918c8393982b'

def insert_data(sid, pr_hit_url, pr_screen_time):
    pr_create_time = pr_screen_time
    pr_update_time = pr_screen_time
    mysql_helper = MysqlHelper.Helper()
    insert_sql = "insert into patrol_result_common(sid, pr_origin, pr_hit_type, pr_hit_url, pr_screen_time, pr_module_name, pr_status, pr_repair, pr_operator_sid, pr_create_time, pr_update_time, pr_deleted, pr_ti_sid, pr_cpi_sid, pr_ai_sid, pr_ci_sid) values(%s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s)"
    insert_params = (sid, pr_origin, pr_hit_type, pr_hit_url, pr_screen_time, pr_module_name, pr_status, pr_repair, pr_operator_sid, pr_create_time, pr_update_time, pr_deleted, pr_ti_sid, pr_cpi_sid, pr_ai_sid, pr_ci_sid)
    mysql_helper.insert(insert_sql, insert_params)

def encryption(signature_data):
    signature_md5 = hashlib.md5()
    signature_md5.update(signature_data.encode("utf-8"))
    signature = signature_md5.hexdigest()
    return signature

def image_to_bytes(image):
    imgByteArr = io.BytesIO()
    image.save(imgByteArr, format='PNG')
    img_data = imgByteArr.getvalue()
    return img_data

def IPhone_save_data(c, mode):
    image = c.screenshot()
    image_bytes = image_to_bytes(image)
    sid = encryption(mode + datetime.datetime.now().strftime("%Y%m%d_%H%M%S.%f"))
    img_name = sid + '.jpg'
    img_url = Helper.save1_image_file_by_bytes(save_name='/app_patrol_main_img/app_patrol_'+ mode +'_' + img_name, file_data=image_bytes)
    insert_data(sid=sid, pr_hit_url=img_url, pr_screen_time=time.strftime("%Y-%m-%d %H:%M:%S", time.localtime()))
    return img_url

def folder(filename):
    filepath = folderpath + time.strftime("%Y-%m-%d", time.localtime()) + '/' + filename
    pathlib.Path(filepath).parent.mkdir(parents=True, exist_ok=True)
    return filepath

def write_csv(file_name, image_url):
    ssy_t = open(folder(filename=file_name+'.csv'), mode='a+')
    ssy_t.write(file_name+',' + image_url + '\n')
    ssy_t.close()


def run():
    os.popen("tidevice -u " + udid + " wdaproxy -B " + wda_proxy + " --port " + port)
    time.sleep(5)
    c = wda.Client('http://localhost:' + port)
    strat_flag = True
    while True:
        try:
            s = c.session('com.tvbc.maiduidui')
            if strat_flag:
                IOSWatcherUtils.Watcher(s=c, selector='xpath=\'//Button[@label="close"]\'', name='click_close')
                IOSWatcherUtils.Watcher(s=c, selector='xpath=\'//*[@text="我知道了"]\'', name='click_close_knew')
                IOSWatcherUtils.Watcher(s=c, selector='xpath=\'//*[@label="关闭"]\'', name='click_close_s')
                IOSWatcherUtils.Watchers.start(name='click_close')
                IOSWatcherUtils.Watchers.start(name='click_close_knew')
                IOSWatcherUtils.Watchers.start(name='click_close_s')
                strat_flag = False
            # 开屏广告
            for i in range(0, 10):
                if c(predicate='name LIKE "*跳过*"').exists:
                    IOS_kp_img_url = IPhone_save_data(c=c, mode='IOS_kp')
                    write_csv(file_name='IOS_开屏广告.csv', image_url=IOS_kp_img_url)
                    print('IOS_开屏广告: ' + IOS_kp_img_url)
                    break
                else:
                    time.sleep(1)
            nx = 0
            while not c(xpath='//*[@label="首页"]').exists:
                time.sleep(1)
                nx += 1
                if nx >= 5:
                    break
            # 首页广告
            for i in range(20):
                if c(xpath='//StaticText[1]').child(predicate='name LIKE "*广告*"').exists:
                    IOS_sy_img_url = IPhone_save_data(c=c, mode='IOS_sy')
                    write_csv(file_name='IOS_首页广告.csv', image_url=IOS_sy_img_url)
                    print('IOS_首页广告: ' + IOS_sy_img_url)
                    break
                else:
                    time.sleep(1)
            # 搜索页广告
            # c(xpath='//*[@name="home_topbar_search_button"]').click()
            # time.sleep(2)
            # for i in range(0, 5):
            #     if c(predicate='name LIKE "*广告*"').exists:
            #         IOS_ssy_img_url = IPhone_save_data(c=c, mode='IOS_ssy')
            #         write_csv(file_name='IOS_搜索页广告.csv', image_url=IOS_ssy_img_url)
            #         print('IOS_搜索页广告: ' + IOS_ssy_img_url)
            #         break
            #     else:
            #         time.sleep(1)
            # c(xpath='//*[@name="search_cancel_button"]').click()
            time.sleep(3)
            c(xpath='//*[@label="电视剧"]').click()
            time.sleep(1)
            c(xpath='//*[@label="更多"]').click()
            time.sleep(1)
            nx = 0
            while not c(xpath='//*[@label="navigation bar back button" or @label="navigation bar back icon"]').exists:
                time.sleep(1)
                nx += 1
                if nx >= 20:
                    break
            cells = c(xpath='//Cell').find_elements()
            cells[random.randint(1, 2)].click()
            e = c(xpath='//StaticText[@label="06:00"]|//Button[@label="VIP可关闭广告"]|//*[@label="试看中，登录后可享受精彩剧集"]').wait(
                timeout=30.0)
            if e is not None:
                vedio_down_ele_flag = c(xpath='//Other/following-sibling::Other[1]').child(predicate='name LIKE "*广告*"').wait(
                    timeout=10.0)
                time.sleep(2)
                # 播放器下广告
                if vedio_down_ele_flag is not None:
                    IOS_xxl_img_url = IPhone_save_data(c=c, mode='IOS_xxl')
                    write_csv(file_name='IOS_播放器下广告.csv', image_url=IOS_xxl_img_url)
                    print('IOS_播放器下广告: ' + IOS_xxl_img_url)
                # 前贴广告
                if c(xpath='//Button[@label="VIP可关闭广告"]').exists:
                    IOS_qt_img_url = IPhone_save_data(c=c, mode='IOS_qt')
                    write_csv(file_name='IOS_前贴广告.csv', image_url=IOS_qt_img_url)
                    print('IOS_前贴广告: ' + IOS_qt_img_url)
                # 暂停广告
                elif c(xpath='//*[@label="试看中，登录后可同步相关权益"]|//*[@label="试看中，登录后可享受精彩剧集"]').exists:
                    # vedio_ele = s(xpath='//*[@label="重试"]').get(timeout=5.0)
                    # s.double_tap(vedio_ele.bounds.x, vedio_ele.bounds.y)
                    c.double_tap(0.5, 0.2)
                    nx = 0
                    while not c(xpath='//Button[@label="third ad feed close btn dark"]').exists:
                        time.sleep(1)
                        nx += 1
                        if nx >= 20:
                            break
                    if c(xpath='//Button[@label="third ad feed close btn dark"]').exists:
                        IOS_zt_img_url = IPhone_save_data(c=c, mode='IOS_zt')
                        write_csv(file_name='IOS_暂停广告.csv', image_url=IOS_zt_img_url)
                        print('IOS_暂停广告: ' + IOS_zt_img_url)
            s.close()
        except Exception as e:
            traceback.print_exc()
            os.popen("tidevice -u " + udid + " wdaproxy -B " + wda_proxy + " --port 9401")
            time.sleep(5)
            c = wda.Client('http://localhost:' + port)

if __name__ == '__main__':
    run()
