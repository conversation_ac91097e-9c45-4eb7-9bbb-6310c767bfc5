# -*- coding: utf-8 -*-
# !/usr/bin/env python
# coding=utf-8
import hashlib
import pathlib
import random
import time
import datetime
import traceback

import cv2

from src.utils import MysqlHelper
from src.utils.MinioUtils import Helper
import base64
import json
import requests
import codecs
import uiautomator2 as u2

folderpath = r'/Users/<USER>/Downloads/botSmart-日常业务/app巡查/mdd/'
sid = ''
device = 'e9633fdf'
pr_origin = '1'
pr_hit_type = '0'
pr_hit_url = ''
pr_screen_time = '%Y-%m-%d %H:%M:%S'
pr_module_name = '广告'
pr_status = '0'
pr_repair = '0'
pr_operator_sid = 'kGNzHYK6I9srBgf8X9PbKVrGpgKk4jry2XVs'
pr_create_time = '%Y-%m-%d %H:%M:%S'
pr_update_time = '%Y-%m-%d %H:%M:%S'
pr_deleted = '0'
pr_ti_sid = '9bb2acb81e56819db353600030683b7a'
pr_cpi_sid = '67e7d810d77c15bb379f39f411a5bad5'
pr_ai_sid = '55b6a12e3e593e427402b0615fb9aa1c'
pr_ci_sid = '7a124f45e63416eeb6e8918c8393982b'
def add(key, data_id, content):
    data = {
        'data_type': '1',
        'key': key,
        'data_id': data_id,
        'data': content
    }
    return codecs.decode(requests.post(url='http://114.116.242.57:7082/image_politics/add', headers={'Content-type': 'application/json'}, data=json.dumps(data)).text, 'unicode_escape')

def encryption(signature_data):
    signature_md5 = hashlib.md5()
    signature_md5.update(signature_data.encode("utf-8"))
    signature = signature_md5.hexdigest()
    return signature

def imgConvertBase64(img):
    retval, buffer = cv2.imencode('.jpg', img)
    if retval:
        return base64.b64encode(buffer)
    else:
        return None

def insert_data(sid, pr_hit_url, pr_screen_time):
    pr_create_time = pr_screen_time
    pr_update_time = pr_screen_time
    mysql_helper = MysqlHelper.Helper()
    insert_sql = "insert into patrol_result_common(sid, pr_origin, pr_hit_type, pr_hit_url, pr_screen_time, pr_module_name, pr_status, pr_repair, pr_operator_sid, pr_create_time, pr_update_time, pr_deleted, pr_ti_sid, pr_cpi_sid, pr_ai_sid, pr_ci_sid) values(%s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s)"
    insert_params = (sid, pr_origin, pr_hit_type, pr_hit_url, pr_screen_time, pr_module_name, pr_status, pr_repair, pr_operator_sid, pr_create_time, pr_update_time, pr_deleted, pr_ti_sid, pr_cpi_sid, pr_ai_sid, pr_ci_sid)
    mysql_helper.insert(insert_sql, insert_params)

def folder(filename):
    filepath = folderpath + time.strftime("%Y-%m-%d", time.localtime()) + '/' + filename
    pathlib.Path(filepath).parent.mkdir(parents=True, exist_ok=True)
    return filepath

def save_url(mode, img):
    sid = encryption(mode + datetime.datetime.now().strftime("%Y%m%d_%H%M%S.%f"))
    img_name = sid + '.jpg'
    img_url = Helper.save1_image_file_by_base64(save_name='/app_patrol_main_img/app_patrol_'+ mode +'_' + img_name, file_data=imgConvertBase64(img))
    insert_data(sid=sid, pr_hit_url=img_url, pr_screen_time=time.strftime("%Y-%m-%d %H:%M:%S", time.localtime()))
    return img_url

def save2_url(img, cropImg_name):
    img_url = Helper.save1_image_file_by_base64(save_name='/mdd/comparison/'+ cropImg_name, file_data=imgConvertBase64(img))
    return img_url

def run():
    d = u2.connect(device)
    d.watcher.when(xpath='//*[@ text="提交"]').when(xpath='//*[@resource-id="com.tvbc.maiduidui:id/backImg"]').click()
    d.watcher.when(xpath='//*[@resource-id="com.heytap.market:id/downloader_page_action_bar"]').press("back")
    d.watcher.when(xpath='//*[@resource-id="android:id/content"]/android.widget.FrameLayout[1]/android.widget.FrameLayout[1]/android.widget.TextView[1]').press("back")
    d.watcher.when(xpath='//*[@resource-id="com.heytap.market:id/red_dot"]').press("back")
    d.watcher.when(xpath='//*[@resource-id="com.tvbc.maiduidui:id/tv_top_vod_name"]').when(xpath='//*[@resource-id="com.tvbc.maiduidui:id/confirm_button"]').click()
    d.watcher.when(xpath='//*[@text="埋堆堆青少年模式"]').when(xpath='//*[@resource-id="com.tvbc.maiduidui:id/tv_ok"]').click()
    d.watcher.when(xpath='//*[@resource-id="com.android.systemui:id/alertTitle"]').when(xpath='//*[@text="传输文件"]').click()
    d.watcher.start(2.0)
    while True:
        try:
            d.app_start('com.tvbc.maiduidui', stop=True)
            for i in range(0, 10):
                if len(d.xpath('//*[@resource-id="com.byted.pangle:id/tt_splash_skip_btn" or @text="跳过 " or @text="跳过" or @resource-id="com.tvbc.maiduidui:id/iv_slogan"]').all()) >= 1:
                    kp_image = d.screenshot(format='opencv')
                    kp_img_url = save_url(mode='kp', img=kp_image)
                    time.sleep(1)
                    kp_image2 = d.screenshot(format='opencv')
                    kp_img_url2 = save_url(mode='kp', img=kp_image2)
                    #  初筛保存本地
                    kp = open(folder(filename='开屏_初筛广告.csv'), mode='a+')
                    kp.write('开屏_初筛广告,' + kp_img_url + '\n')
                    kp.write('开屏_初筛广告,' + kp_img_url2 + '\n')
                    kp.close()
                    kp_re = add('mdd_search_entry', kp_img_url, kp_img_url)
                    print(kp_re)
                    if json.loads(kp_re)['code'] == '201':
                        if float(json.loads(kp_re)['result'][0]['rate']) < 0.6:
                            #  过筛保存本地
                            kp_t = open(folder(filename='开屏_过筛广告.csv'), mode='a+')
                            kp_t.write('开屏_过筛广告,' + kp_img_url + '\n')
                            kp_t.close()
                            print('开屏: '+kp_img_url)
                    else:
                        #  过筛保存本地
                        kp_t = open(folder(filename='开屏_过筛广告.csv'), mode='a+')
                        kp_t.write('开屏_过筛广告,' + kp_img_url + '\n')
                        kp_t.close()
                        print('开屏: '+kp_img_url)
                    break
                else:
                    d.sleep(1)
            d.sleep(6)
            '''
            # 搜索页广告
            d.xpath('//*[@resource-id="com.tvbc.maiduidui:id/to_search_button"]').click()
            time.sleep(2)
            d.press("back")
            time.sleep(2)
            if d.xpath('//*[@resource-id="com.tvbc.maiduidui:id/vUMTFeedAdView"]').exists:
                ssy_image = d.screenshot(format='opencv')
                ssy_img_url = save_url(mode='ssy', img=ssy_image)
                time.sleep(2)
                ssy_image2 = d.screenshot(format='opencv')
                ssy_img_url2 = save_url(mode='ssy', img=ssy_image2)
                #  初筛保存本地
                ssy = open(folder(filename='搜索页_初筛广告.csv'), mode='a+')
                ssy.write('搜索页_初筛广告,' + ssy_img_url + '\n')
                ssy.write('搜索页_初筛广告,' + ssy_img_url2 + '\n')
                ssy.close()
                #  提取比对图片
                cut_image_bounds = d.xpath('//*[@resource-id="com.tvbc.maiduidui:id/vUMTFeedAdView"]').bounds
                ssy_cropImg = ssy_image[cut_image_bounds[1]:cut_image_bounds[3], cut_image_bounds[0]:cut_image_bounds[2]]
                ssy_cropImg_name = time.strftime("%Y%m%d_%H%M%S", time.localtime()) + '.jpg'
                ssy_cropImg_url = save2_url(img=ssy_cropImg, cropImg_name=ssy_cropImg_name)
                ssy_re = add('mdd_search_entry', ssy_cropImg_url, ssy_cropImg_url)
                print(ssy_re)
                if json.loads(ssy_re)['code'] == '201':
                    # Helper.delete1('/mdd/comparison/' + ssy_cropImg_name)
                    if float(json.loads(ssy_re)['result'][0]['rate']) < 0.6:
                        #  过筛保存本地
                        ssy_t = open(folder(filename='搜索页_过筛广告.csv'), mode='a+')
                        ssy_t.write('搜索页_过筛广告,' + ssy_img_url + '\n')
                        ssy_t.close()
                        print('搜索页: ' + ssy_img_url)
                else:
                    # Helper.delete1('/mdd/comparison/' + ssy_cropImg_name)
                    #  过筛保存本地
                    ssy_t = open(folder(filename='搜索页_过筛广告.csv'), mode='a+')
                    ssy_t.write('搜索页_过筛广告,' + ssy_img_url + '\n')
                    ssy_t.close()
                    print('搜索页: ' + ssy_img_url)
                    d.press("back")
                    d.sleep(2)
            else:
                d.press("back")
                d.sleep(2)
            '''
            for i in range(0, 15):
                if len(d.xpath('//*[@resource-id="com.tvbc.maiduidui:id/icon_close_ad" or @resource-id="com.tvbc.maiduidui:id/banner_flag_mo_text"]').all()) >= 1:
                    sy_image = d.screenshot(format='opencv')
                    time.sleep(1)
                    sy_image2 = d.screenshot(format='opencv')
                    sy_img_url = save_url(mode='sy', img=sy_image)
                    sy_img_url2 = save_url(mode='sy', img=sy_image2)

                    #  初筛保存本地
                    sy = open(folder(filename='首页_初筛广告.csv'), mode='a+')
                    sy.write('首页_初筛广告,' + sy_img_url + '\n')
                    sy.write('首页_初筛广告,' + sy_img_url2 + '\n')
                    sy.close()
                    #  提取比对图片
                    cut_image_bounds = d.xpath('//*[@resource-id="com.tvbc.maiduidui:id/layout_banner_content"]').bounds
                    sy_cropImg = sy_image[cut_image_bounds[1]:cut_image_bounds[3], cut_image_bounds[0]:cut_image_bounds[2]]
                    sy_cropImg_name = time.strftime("%Y%m%d_%H%M%S", time.localtime()) + '.jpg'
                    sy_cropImg_url = save2_url(img=sy_cropImg, cropImg_name=sy_cropImg_name)
                    sy_re = add('mdd_search_entry', sy_img_url, sy_cropImg_url)
                    print(sy_re)
                    if json.loads(sy_re)['code'] == '201':
                        # Helper.delete1('/mdd/comparison/' + sy_cropImg_name)
                        if float(json.loads(sy_re)['result'][0]['rate']) < 0.6:
                            #  过筛保存本地
                            sy_t = open(folder(filename='首页_过筛广告.csv'), mode='a+')
                            sy_t.write('首页_过筛广告,' + sy_img_url + '\n')
                            sy_t.close()
                            print('首页: '+sy_img_url)
                    else:
                        # Helper.delete1('/mdd/comparison/' + sy_cropImg_name)
                        #  过筛保存本地
                        sy_t = open(folder(filename='首页_过筛广告.csv'), mode='a+')
                        sy_t.write('首页_过筛广告,' + sy_img_url + '\n')
                        sy_t.close()
                        print('首页: '+sy_img_url)
                    break
                else:
                    d.swipe(0.8, 0.25, 0.2, 0.23, 0.2)
                    d.sleep(2)
            time_flag = 0
            while time_flag < 10:
                d.swipe_ext("up")
                time.sleep(2)
                time_flag += 1
                if time_flag < 2:
                    continue
                if d.xpath('//*[@resource-id="com.tvbc.maiduidui:id/vUMTFeedAdView"]').exists:
                    pbl_image = d.screenshot(format='opencv')
                    pbl_img_url = save_url(mode='pbl', img=pbl_image)
                    pbl_image2 = d.screenshot(format='opencv')
                    pbl_img_url2 = save_url(mode='pbl', img=pbl_image2)
                    #  初筛保存本地
                    pbl = open(folder(filename='首页-信息流_初筛广告.csv'), mode='a+')
                    pbl.write('首页-信息流_初筛广告,' + pbl_img_url + '\n')
                    pbl.write('首页-信息流_初筛广告,' + pbl_img_url2 + '\n')
                    pbl.close()
                    #  提取比对图片
                    cut_image_bounds = d.xpath('//*[@resource-id="com.tvbc.maiduidui:id/vUMTFeedAdView"]').bounds
                    pbl_cropImg = pbl_image[cut_image_bounds[1]:cut_image_bounds[3], cut_image_bounds[0]:cut_image_bounds[2]]
                    pbl_cropImg_name = time.strftime("%Y%m%d_%H%M%S", time.localtime()) + '.jpg'
                    pbl_cropImg_url = save2_url(img=pbl_cropImg, cropImg_name=pbl_cropImg_name)
                    pbl_re = add('mdd_search_entry', pbl_img_url, pbl_cropImg_url)
                    print(pbl_re)
                    if json.loads(pbl_re)['code'] == '201':
                        # Helper.delete1('/mdd/comparison/' + pbl_cropImg_name)
                        if float(json.loads(pbl_re)['result'][0]['rate']) < 0.6:
                            #  过筛保存本地
                            pbl_t = open(folder(filename='首页-信息流_过筛广告.csv'), mode='a+')
                            pbl_t.write('首页-信息流_过筛广告,' + pbl_img_url + '\n')
                            pbl_t.close()
                            print('首页-信息流: '+pbl_img_url)
                    else:
                        # Helper.delete1('/mdd/comparison/' + pbl_cropImg_name)
                        #  过筛保存本地
                        pbl_t = open(folder(filename='首页-信息流_过筛广告.csv'), mode='a+')
                        pbl_t.write('首页-信息流_过筛广告,' + pbl_img_url + '\n')
                        pbl_t.close()
                        print('首页-信息流: '+pbl_img_url)
                    break
            # 观看历史
            d.xpath('//*[@resource-id="com.tvbc.maiduidui:id/tab_container"]/descendant::android.widget.TextView[@resource-id="com.tvbc.maiduidui:id/tv_tab_name"][last()]').click()
            time_flag = 0
            while ((not d.xpath('//*[@resource-id="com.tvbc.maiduidui:id/item_play_history"]').exists and not d.xpath('//*[@resource-id="com.tvbc.maiduidui:id/item_play_history_title_no_data_bar"]').exists) or not d.xpath('//*[@resource-id="com.tvbc.maiduidui:id/vUMTFeedAdView"]').exists) and time_flag < 10:
                time.sleep(1)
                time_flag += 1
            if time_flag < 10:
                gkls_image = d.screenshot(format='opencv')
                gkls_img_url = save_url(mode='gkls', img=gkls_image)
                gkls_image2 = d.screenshot(format='opencv')
                gkls_img_url2 = save_url(mode='gkls', img=gkls_image2)
                #  初筛保存本地
                gkls = open(folder(filename='我的页-观看历史_初筛广告.csv'), mode='a+')
                gkls.write('我的页-观看历史_初筛广告,' + gkls_img_url + '\n')
                gkls.write('我的页-观看历史_初筛广告,' + gkls_img_url2 + '\n')
                gkls.close()
                #  提取比对图片
                cut_image_bounds = d.xpath('//*[@resource-id="com.tvbc.maiduidui:id/vUMTFeedAdView"]').bounds
                gkls_cropImg = gkls_image[cut_image_bounds[1]:cut_image_bounds[3],
                              cut_image_bounds[0]:cut_image_bounds[2]]
                gkls_cropImg_name = time.strftime("%Y%m%d_%H%M%S", time.localtime()) + '.jpg'
                gkls_cropImg_url = save2_url(img=gkls_cropImg, cropImg_name=gkls_cropImg_name)
                gkls_re = add('mdd_search_entry', gkls_img_url, gkls_cropImg_url)
                print(gkls_re)
                if json.loads(gkls_re)['code'] == '201':
                    # Helper.delete1('/mdd/comparison/' + gkls_cropImg_name)
                    if float(json.loads(gkls_re)['result'][0]['rate']) < 0.6:
                        #  过筛保存本地
                        gkls_t = open(folder(filename='我的页-观看历史_过筛广告.csv'), mode='a+')
                        gkls_t.write('我的页-观看历史_过筛广告,' + gkls_img_url + '\n')
                        gkls_t.close()
                        print('我的页-观看历史: ' + gkls_img_url)
                else:
                    # Helper.delete1('/mdd/comparison/' + gkls_cropImg_name)
                    #  过筛保存本地
                    gkls_t = open(folder(filename='我的页-观看历史_过筛广告.csv'), mode='a+')
                    gkls_t.write('我的页-观看历史_过筛广告,' + gkls_img_url + '\n')
                    gkls_t.close()
                    print('我的页-观看历史: ' + gkls_img_url)
        except Exception as e:
            print(time.strftime("%Y-%m-%d %H:%M:%S", time.localtime()))
            traceback.print_exc()
            d.sleep(2)

if __name__ == '__main__':
    run()