# -*- coding: utf-8 -*-
# !/usr/bin/env python
# coding=utf-8
import hashlib
import pathlib
import random
import time
import datetime
import traceback

import cv2

from src.utils import MysqlHelper
from src.utils.MinioUtils import Helper
import base64
import json
import requests
import codecs
import uiautomator2 as u2

folderpath = r'/Users/<USER>/Downloads/botSmart-日常业务/app巡查/mdd/'
sid = ''
device = 'e9633fdf'
pr_origin = '1'
pr_hit_type = '0'
pr_hit_url = ''
pr_screen_time = '%Y-%m-%d %H:%M:%S'
pr_module_name = '广告'
pr_status = '0'
pr_repair = '0'
pr_operator_sid = 'kGNzHYK6I9srBgf8X9PbKVrGpgKk4jry2XVs'
pr_create_time = '%Y-%m-%d %H:%M:%S'
pr_update_time = '%Y-%m-%d %H:%M:%S'
pr_deleted = '0'
pr_ti_sid = '9bb2acb81e56819db353600030683b7a'
pr_cpi_sid = '67e7d810d77c15bb379f39f411a5bad5'
pr_ai_sid = '55b6a12e3e593e427402b0615fb9aa1c'
pr_ci_sid = '7a124f45e63416eeb6e8918c8393982b'
def add(key, data_id, content):
    data = {
        'data_type': '1',
        'key': key,
        'data_id': data_id,
        'data': content
    }
    return codecs.decode(requests.post(url='http://114.116.242.57:7082/image_politics/add', headers={'Content-type': 'application/json'}, data=json.dumps(data)).text, 'unicode_escape')

def encryption(signature_data):
    signature_md5 = hashlib.md5()
    signature_md5.update(signature_data.encode("utf-8"))
    signature = signature_md5.hexdigest()
    return signature

def imgConvertBase64(img):
    retval, buffer = cv2.imencode('.jpg', img)
    if retval:
        return base64.b64encode(buffer)
    else:
        return None

def insert_data(sid, pr_hit_url, pr_screen_time):
    pr_create_time = pr_screen_time
    pr_update_time = pr_screen_time
    mysql_helper = MysqlHelper.Helper()
    insert_sql = "insert into patrol_result_common(sid, pr_origin, pr_hit_type, pr_hit_url, pr_screen_time, pr_module_name, pr_status, pr_repair, pr_operator_sid, pr_create_time, pr_update_time, pr_deleted, pr_ti_sid, pr_cpi_sid, pr_ai_sid, pr_ci_sid) values(%s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s)"
    insert_params = (sid, pr_origin, pr_hit_type, pr_hit_url, pr_screen_time, pr_module_name, pr_status, pr_repair, pr_operator_sid, pr_create_time, pr_update_time, pr_deleted, pr_ti_sid, pr_cpi_sid, pr_ai_sid, pr_ci_sid)
    mysql_helper.insert(insert_sql, insert_params)

def folder(filename):
    filepath = folderpath + time.strftime("%Y-%m-%d", time.localtime()) + '/' + filename
    pathlib.Path(filepath).parent.mkdir(parents=True, exist_ok=True)
    return filepath

def save_url(mode, img):
    sid = encryption(mode + datetime.datetime.now().strftime("%Y%m%d_%H%M%S.%f"))
    img_name = sid + '.jpg'
    img_url = Helper.save1_image_file_by_base64(save_name='/app_patrol_main_img/app_patrol_'+ mode +'_' + img_name, file_data=imgConvertBase64(img))
    insert_data(sid=sid, pr_hit_url=img_url, pr_screen_time=time.strftime("%Y-%m-%d %H:%M:%S", time.localtime()))
    return img_url

def save2_url(img, cropImg_name):
    img_url = Helper.save1_image_file_by_base64(save_name='/mdd/comparison/'+ cropImg_name, file_data=imgConvertBase64(img))
    # insert_data(sid=sid, pr_hit_url=img_url, pr_screen_time=time.strftime("%Y-%m-%d %H:%M:%S", time.localtime()))
    return img_url

def run():
    d = u2.connect(device)
    d.watcher.when(xpath='//*[@text="提交"]').when(xpath='//*[@resource-id="com.tvbc.maiduidui:id/backImg"]').click()
    d.watcher.when(xpath='//*[@resource-id="com.heytap.market:id/downloader_page_action_bar"]').press("back")
    d.watcher.when(xpath='//*[@resource-id="android:id/content"]/android.widget.FrameLayout[1]/android.widget.FrameLayout[1]/android.widget.TextView[1]').press("back")
    d.watcher.when(xpath='//*[@resource-id="com.heytap.market:id/red_dot"]').press("back")
    d.watcher.when(xpath='//*[@resource-id="com.tvbc.maiduidui:id/tv_top_vod_name"]').when(xpath='//*[@resource-id="com.tvbc.maiduidui:id/confirm_button"]').click()
    d.watcher.when(xpath='//*[@text="埋堆堆青少年模式"]').when(xpath='//*[@resource-id="com.tvbc.maiduidui:id/tv_ok"]').click()
    d.watcher.when(xpath='//*[@resource-id="com.android.systemui:id/alertTitle"]').when(xpath='//*[@text="传输文件"]').click()
    d.watcher.start(2.0)
    while True:
        try:
            d.app_start('com.tvbc.maiduidui', stop=True)
            d.sleep(10)
            flag = False
            # d.swipe_ext("up")
            # d.sleep(1)
            dd_n = 0
            while True:
                if len(d.xpath('//*[@text="首页"]').all()) > 0:
                    break
                time.sleep(1)
                dd_n += 1
                if dd_n >= 5:
                    break
            nx = str(random.randrange(2, 4))
            d.xpath('//*[@resource-id="com.tvbc.maiduidui:id/tabs"]/descendant::android.widget.TextView[@resource-id="com.tvbc.maiduidui:id/tv_tab_title"]['+nx+']').click()
            d.xpath('//*[@resource-id="com.tvbc.maiduidui:id/module_title_more_view"]').click()
            d.sleep(3)
            fx = 0
            while True:
                if fx >= 3:
                    break
                # elements = d.xpath('//*[@resource-id="com.tvbc.maiduidui:id/tv_vod_name"]').all()
                fx += 1
                elements = d.xpath('//*[@resource-id="com.tvbc.maiduidui:id/recyclerview"]/android.view.ViewGroup|//*[@resource-id="com.tvbc.maiduidui:id/recyclerview"]/android.widget.LinearLayout').all()
                for element in elements:
                    element.click()
                    d.sleep(2)
                    if len(d.xpath('//*[@resource-id="com.tvbc.maiduidui:id/video_name_view"]').all()) == 0:
                        d.sleep(1)
                        flag = True
                        break
                    # 检测 播放器-前贴广告
                    for i in range(0, 10):
                        if len(d.xpath('//*[@resource-id="com.tvbc.maiduidui:id/tx_vip_close_touch"]').all()) != 0:
                            qt_image = d.screenshot(format='opencv')
                            qt_img_url = save_url(mode='qt', img=qt_image)
                            time.sleep(5)
                            qt_image2 = d.screenshot(format='opencv')
                            qt_img_url2 = save_url(mode='qt', img=qt_image2)
                            #  初筛保存本地
                            qt = open(folder(filename='播放器-前贴_初筛广告.csv'), mode='a+')
                            qt.write('播放器-前贴_初筛广告,' + qt_img_url + '\n')
                            qt.write('播放器-前贴_初筛广告,' + qt_img_url2 + '\n')
                            qt.close()
                            #  提取比对图片
                            cut_image_bounds = d.xpath('//*[@resource-id="com.tvbc.maiduidui:id/control_view_layout"]').bounds
                            qt_cropImg = qt_image[cut_image_bounds[1]:cut_image_bounds[3], cut_image_bounds[0]:cut_image_bounds[2]]
                            qt_cropImg_name = time.strftime("%Y%m%d_%H%M%S", time.localtime()) + '.jpg'
                            qt_cropImg_url = save2_url(img=qt_cropImg, cropImg_name=qt_cropImg_name)
                            qt_re = add('mdd_search_entry', qt_img_url, qt_cropImg_url)
                            print(qt_re)
                            if json.loads(qt_re)['code'] == '201':
                                # Helper.delete1('/mdd/comparison/' + qt_cropImg_name)
                                if float(json.loads(qt_re)['result'][0]['rate']) < 0.6:
                                    #  过筛保存本地
                                    qt_t = open(folder(filename='播放器-前贴_过筛广告.csv'), mode='a+')
                                    qt_t.write('播放器-前贴_过筛广告,' + qt_img_url + '\n')
                                    qt_t.close()
                                    print('前贴: '+qt_img_url)
                            else:
                                # Helper.delete1('/mdd/comparison/' + qt_cropImg_name)
                                #  过筛保存本地
                                qt_t = open(folder(filename='播放器-前贴_过筛广告.csv'), mode='a+')
                                qt_t.write('播放器-前贴_过筛广告,' + qt_img_url + '\n')
                                qt_t.close()
                                print('前贴: '+qt_img_url)
                            break
                        else:
                            d.sleep(1)
                    # 检测 播放器下广告
                    for i in range(0, 10):
                        # if len(d.xpath('//*[@resource-id="com.tvbc.maiduidui:id/ms_info_flow_ad_view"]').all()) != 0:
                        if len(d.xpath('//*[@resource-id="com.tvbc.maiduidui:id/layout_ad_group_full"]/descendant::android.widget.FrameLayout[@resource-id="com.tvbc.maiduidui:id/vUMTFeedAdView"]').all()) != 0:
                            xxl_image = d.screenshot(format='opencv')
                            xxl_img_url = save_url(mode='xxl', img=xxl_image)
                            time.sleep(2)
                            xxl_image2 = d.screenshot(format='opencv')
                            xxl_img_url2 = save_url(mode='xxl', img=xxl_image2)
                            #  初筛保存本地
                            xxl = open(folder(filename='播放器下_初筛广告.csv'), mode='a+')
                            xxl.write('播放器下_初筛广告,' + xxl_img_url + '\n')
                            xxl.write('播放器下_初筛广告,' + xxl_img_url2 + '\n')
                            xxl.close()
                            #  提取比对图片
                            cut_image_bounds = d.xpath('//*[@resource-id="com.tvbc.maiduidui:id/layout_ad_group_full"]').bounds
                            xxl_cropImg = xxl_image[cut_image_bounds[1]:cut_image_bounds[3], cut_image_bounds[0]:cut_image_bounds[2]]
                            xxl_cropImg_name = time.strftime("%Y%m%d_%H%M%S", time.localtime()) + '.jpg'
                            xxl_cropImg_url = save2_url(img=xxl_cropImg, cropImg_name=xxl_cropImg_name)
                            xxl_re = add('mdd_search_entry', xxl_img_url, xxl_cropImg_url)
                            print(xxl_re)
                            if json.loads(xxl_re)['code'] == '201':
                                # Helper.delete1('/mdd/comparison/' + xxl_cropImg_name)
                                if float(json.loads(xxl_re)['result'][0]['rate']) < 0.6:
                                    #  过筛保存本地
                                    xxl_t = open(folder(filename='播放器下_过筛广告.csv'), mode='a+')
                                    xxl_t.write('播放器下_过筛广告,' + xxl_img_url + '\n')
                                    xxl_t.close()
                                    print('播放器下: '+xxl_img_url)
                            else:
                                # Helper.delete1('/mdd/comparison/' + xxl_cropImg_name)
                                #  过筛保存本地
                                xxl_t = open(folder(filename='播放器下_过筛广告.csv'), mode='a+')
                                xxl_t.write('播放器下_过筛广告,' + xxl_img_url + '\n')
                                xxl_t.close()
                                print('播放器下: '+xxl_img_url)
                            break
                        else:
                            d.sleep(1)
                    # 检测 播放器-暂停广告
                    for i in range(0, 30):
                        if len(d.xpath('//*[@resource-id="com.tvbc.maiduidui:id/tx_vip_close_touch" or @resource-id="com.tvbc.maiduidui:id/to_open_vip_button"]').all()) == 0:
                            d.double_click(0.5, 0.2, 0.1)
                            d.sleep(3)
                            for n in range(0, 10):
                                if len(d.xpath('//*[@resource-id="com.tvbc.maiduidui:id/layout_parent_ad" or @resource-id="com.tvbc.maiduidui:id/ivLogo"]').all()) != 0:
                                    zt_image = d.screenshot(format='opencv')
                                    zt_img_url = save_url(mode='zt', img=zt_image)
                                    time.sleep(4)
                                    zt_image2 = d.screenshot(format='opencv')
                                    zt_img_url2 = save_url(mode='zt', img=zt_image2)
                                    #  初筛保存本地
                                    zt = open(folder(filename='播放器-暂停_初筛广告.csv'), mode='a+')
                                    zt.write('播放器-暂停_初筛广告,' + zt_img_url + '\n')
                                    zt.write('播放器-暂停_初筛广告,' + zt_img_url2 + '\n')
                                    zt.close()
                                    #  提取比对图片
                                    cut_image_bounds = d.xpath('//*[@resource-id="com.tvbc.maiduidui:id/layout_parent_ad"]').bounds
                                    zt_cropImg = zt_image[cut_image_bounds[1]:cut_image_bounds[3], cut_image_bounds[0]:cut_image_bounds[2]]
                                    zt_cropImg_name = time.strftime("%Y%m%d_%H%M%S", time.localtime()) + '.jpg'
                                    zt_cropImg_url = save2_url(img=zt_cropImg, cropImg_name=zt_cropImg_name)
                                    zt_re = add('mdd_search_entry', zt_img_url, zt_cropImg_url)
                                    print(zt_re)

                                    if json.loads(zt_re)['code'] == '201':
                                        # Helper.delete1('/mdd/comparison/' + zt_cropImg_name)
                                        if float(json.loads(zt_re)['result'][0]['rate']) < 0.6:
                                            #  过筛保存本地
                                            zt_t = open(folder(filename='播放器-暂停_过筛广告.csv'), mode='a+')
                                            zt_t.write('播放器-暂停_过筛广告,' + zt_img_url + '\n')
                                            zt_t.close()
                                            print('暂停: '+zt_img_url)
                                    else:
                                        # Helper.delete1('/mdd/comparison/' + zt_cropImg_name)
                                        #  过筛保存本地
                                        zt_t = open(folder(filename='播放器-暂停_过筛广告.csv'), mode='a+')
                                        zt_t.write('播放器-暂停_过筛广告,' + zt_img_url + '\n')
                                        zt_t.close()
                                        print('暂停: '+zt_img_url)
                                    break
                                # elif len(d.xpath('//*[@resource-id="com.tvbc.maiduidui:id/try_watch_text_view" and @text="试看结束，登录后可享受精彩剧集"]|//*[@resource-id="com.tvbc.maiduidui:id/try_watch_text_view" and @text="试看已结束，请开通VIP观看完整版"]').all()) >= 1:
                                #     d.shell('pm clear com.tvbc.maiduidui')
                                #     flag = True
                                #     break
                                else:
                                    d.sleep(1)
                            break
                        else:
                            d.sleep(4)
                    d.press("back")
                    d.sleep(1)
                if flag:
                    break
                d.swipe(388, 2175, 725, 520, 0.5)
                d.sleep(2)
        except Exception as e:
            print(time.strftime("%Y-%m-%d %H:%M:%S", time.localtime()))
            traceback.print_exc()
            d.sleep(2)

if __name__ == '__main__':
    run()